package controller

import (
	"gitee.com/rcztcs/zjl/internal/pkg/utils"
	"gitee.com/rcztcs/zjl/internal/zjl/model"
	"gitee.com/rcztcs/zjl/internal/zjl/svc"
	"gitee.com/rcztcs/zjl/internal/zjl/types"
	"github.com/gin-gonic/gin"
	"time"
)

// registerTaskRoutes 注册任务相关的路由
func registerTaskRoutes(r *gin.RouterGroup, svc *svc.ServiceContext) {
	tasks := r.Group("/tasks")
	{
		// 任务资源管理
		tasks.GET("", ListTasks(svc))                   // GET /tasks - 获取任务列表（支持多种筛选条件）
		tasks.GET("/:id", GetTaskByID(svc))             // GET /tasks/:id - 获取指定任务详情
		tasks.PUT("/:id/status", UpdateTaskStatus(svc)) // PUT /tasks/:id/status - 更新任务状态
	}

	// 创建任务的特殊路由
	task := r.Group("/task")
	{
		task.POST("/create", CreateTask(svc)) // POST /task/create - 创建任务
	}
}

// CreateTask 创建任务
func CreateTask(svc *svc.ServiceContext) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req types.CreateTaskRequest

		err := utils.ParseRequest(c, &req)
		if err != nil {
			utils.ResponseError(c, err)
			return
		}

		// TODO 获取文件

		// 企业信用码
		// TODO
		creditCode := "codeX1"

		// 调用服务层创建任务
		userID, password, err := svc.TaskService.CreateTask(req.ReportID, creditCode)
		if err != nil {
			utils.ResponseError(c, err)
			return
		}

		res := map[string]any{
			"user_id":  userID,
			"password": password,
		}

		utils.ResponseSuccess(c, res)
	}
}

// GetTaskByID 根据ID获取任务详情
func GetTaskByID(svc *svc.ServiceContext) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req types.GetTaskRequest

		// 绑定路径参数
		if err := c.ShouldBindUri(&req); err != nil {
			utils.ResponseError(c, utils.NewAppError(utils.ErrInvalidParamsCode, "参数错误: "+err.Error()))
			return
		}

		// 调用服务层获取任务详情
		task, err := svc.TaskService.GetTaskByID(req.ID)
		if err != nil {
			utils.ResponseError(c, err)
			return
		}

		response := &types.GetTaskResponse{
			Task: task,
		}

		utils.ResponseSuccess(c, response)
	}
}

// ListTasks 获取任务列表（支持多种筛选条件）
func ListTasks(svc *svc.ServiceContext) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req types.ListTasksRequest

		// 绑定查询参数
		if err := c.ShouldBindQuery(&req); err != nil {
			utils.ResponseError(c, utils.NewAppError(utils.ErrInvalidParamsCode, "参数错误: "+err.Error()))
			return
		}

		// 设置默认分页参数
		req.SetDefaultPagination()

		// 解析时间参数
		var startTime, endTime *time.Time
		if startTimeStr := c.Query("start_time"); startTimeStr != "" {
			if t, err := time.Parse("2006-01-02 15:04:05", startTimeStr); err == nil {
				startTime = &t
			}
		}
		if endTimeStr := c.Query("end_time"); endTimeStr != "" {
			if t, err := time.Parse("2006-01-02 15:04:05", endTimeStr); err == nil {
				endTime = &t
			}
		}

		// 根据不同的查询条件调用相应的服务方法
		var tasks []*model.Task
		var total int64
		var err error

		// 检查是否有特定的查询条件
		if req.UserID != nil && req.CreditCode == "" && startTime == nil && endTime == nil {
			// 仅根据用户ID查询
			tasks, total, err = svc.TaskService.ListTasksByUserID(*req.UserID, req.Page, req.PageSize)
		} else if req.CreditCode != "" && req.UserID == nil && startTime == nil && endTime == nil {
			// 仅根据信用代码查询
			tasks, total, err = svc.TaskService.ListTasksByCreditCode(req.CreditCode, req.Page, req.PageSize)
		} else if startTime != nil && endTime != nil && req.UserID == nil && req.CreditCode == "" {
			// 仅根据时间区间查询
			tasks, total, err = svc.TaskService.ListTasksByTimeRange(*startTime, *endTime, req.Page, req.PageSize)
		} else {
			// 综合查询（支持多条件筛选）
			tasks, total, err = svc.TaskService.ListTasks(req.Page, req.PageSize, req.UserID, req.CreditCode, req.Status, startTime, endTime)
		}

		if err != nil {
			utils.ResponseError(c, err)
			return
		}

		response := &types.ListTasksResponse{
			Tasks: tasks,
			Total: total,
		}

		utils.ResponseSuccess(c, response)
	}
}

// UpdateTaskStatus 更新任务状态
func UpdateTaskStatus(svc *svc.ServiceContext) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req types.UpdateTaskStatusRequest

		// 绑定路径参数
		if err := c.ShouldBindUri(&req); err != nil {
			utils.ResponseError(c, utils.NewAppError(utils.ErrInvalidParamsCode, "参数错误: "+err.Error()))
			return
		}

		// 绑定JSON参数
		if err := c.ShouldBindJSON(&req); err != nil {
			utils.ResponseError(c, utils.NewAppError(utils.ErrInvalidParamsCode, "参数错误: "+err.Error()))
			return
		}

		// 调用服务层更新任务状态
		err := svc.TaskService.UpdateTaskStatus(req.ID, req.Status)
		if err != nil {
			utils.ResponseError(c, err)
			return
		}

		// 获取更新后的任务信息
		task, err := svc.TaskService.GetTaskByID(req.ID)
		if err != nil {
			utils.ResponseError(c, err)
			return
		}

		response := &types.UpdateTaskStatusResponse{
			Task: task,
		}

		utils.ResponseSuccess(c, response)
	}
}
