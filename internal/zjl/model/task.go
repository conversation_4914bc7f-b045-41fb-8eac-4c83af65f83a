package model

import "gorm.io/gorm"

// TaskStatus 任务状态枚举
type TaskStatus string

const (
	// TaskStatusPending  待确认
	TaskStatusPending TaskStatus = "pending"
	// TaskStatusReviewing  审核中
	TaskStatusReviewing TaskStatus = "reviewing"
	// TaskStatusFinalReview  待终审
	TaskStatusFinalReview TaskStatus = "final_review"
	// TaskStatusCompleted 已完结
	TaskStatusCompleted TaskStatus = "completed"
)

// Task 任务模型
type Task struct {
	gorm.Model
	UserID     uint       `gorm:"column:user_id;type:int unsigned;not null;index" json:"user_id"`         // 用户ID
	CreditCode string     `gorm:"column:credit_code;type:varchar(100);not null;index" json:"credit_code"` // 信用代码
	Status     TaskStatus `gorm:"column:status;type:varchar(20);not null;index" json:"status"`            // 状态
	ReportID   string     `gorm:"column:report_id;type:varchar(100)" json:"report_id"`                    // 报告ID
}
