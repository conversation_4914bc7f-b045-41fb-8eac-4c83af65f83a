package model

import "gorm.io/gorm"

// UserStatus 用户状态枚举
type UserStatus int

const (
	UserStatusEnabled  UserStatus = 1 // 启用
	UserStatusDisabled UserStatus = 2 // 禁用
)

// User 用户模型
type User struct {
	gorm.Model
	Nickname   string     `gorm:"column:nickname" json:"nickname"`       // 昵称
	Avatar     string     `gorm:"column:avatar" json:"avatar"`           // 头像
	Username   string     `gorm:"column:username" json:"username"`       // 账号
	Password   string     `gorm:"column:password" json:"-"`              // 密码（不返回给前端）
	Mobile     string     `gorm:"column:mobile" json:"mobile"`           // 手机号
	CreditCode string     `gorm:"column:credit_code" json:"credit_code"` // 信用代码
	Status     UserStatus `gorm:"column:status" json:"status"`           // 状态：0-禁用，1-启用
}
