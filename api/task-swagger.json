{"openapi": "3.0.3", "info": {"title": "任务管理API", "description": "任务管理相关的RESTful API接口文档，遵循RESTful设计规范", "version": "1.0.0", "contact": {"name": "API Support", "email": "<EMAIL>"}}, "servers": [{"url": "http://localhost:8080/api", "description": "开发环境"}], "paths": {"/task/create": {"post": {"tags": ["任务管理"], "summary": "创建任务", "description": "创建新的任务", "operationId": "createTask", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateTaskRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/CreateTaskRequest"}}}}, "responses": {"200": {"description": "创建成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SuccessResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/CreateTaskResponse"}}}]}}}}, "400": {"description": "参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "内部服务器错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/tasks": {"get": {"tags": ["任务管理"], "summary": "获取任务列表", "description": "分页获取任务列表，支持多种筛选条件", "operationId": "listTasks", "parameters": [{"name": "page", "in": "query", "required": false, "description": "页码，默认1", "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "page_size", "in": "query", "required": false, "description": "每页数量，默认10", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}}, {"name": "user_id", "in": "query", "required": false, "description": "用户ID筛选", "schema": {"type": "integer", "format": "int64", "minimum": 1}}, {"name": "credit_code", "in": "query", "required": false, "description": "信用代码筛选", "schema": {"type": "string"}}, {"name": "status", "in": "query", "required": false, "description": "任务状态筛选", "schema": {"$ref": "#/components/schemas/TaskStatus"}}, {"name": "start_time", "in": "query", "required": false, "description": "开始时间筛选，格式：2006-01-02 15:04:05", "schema": {"type": "string", "format": "date-time"}}, {"name": "end_time", "in": "query", "required": false, "description": "结束时间筛选，格式：2006-01-02 15:04:05", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SuccessResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/ListTasksResponse"}}}]}}}}, "400": {"description": "参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/tasks/{id}": {"get": {"tags": ["任务管理"], "summary": "获取任务详情", "description": "根据任务ID获取任务详细信息", "operationId": "getTaskById", "parameters": [{"name": "id", "in": "path", "required": true, "description": "任务ID", "schema": {"type": "integer", "format": "int64", "minimum": 1}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SuccessResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/GetTaskResponse"}}}]}}}}, "400": {"description": "参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "任务不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/tasks/{id}/status": {"put": {"tags": ["任务管理"], "summary": "更新任务状态", "description": "更新指定任务的状态", "operationId": "updateTaskStatus", "parameters": [{"name": "id", "in": "path", "required": true, "description": "任务ID", "schema": {"type": "integer", "format": "int64", "minimum": 1}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateTaskStatusRequest"}}}}, "responses": {"200": {"description": "更新成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SuccessResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/UpdateTaskStatusResponse"}}}]}}}}, "400": {"description": "参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "任务不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}}, "components": {"schemas": {"CreateTaskRequest": {"type": "object", "required": ["report_id", "token", "host"], "properties": {"report_id": {"type": "string", "description": "报告ID", "minLength": 1}, "token": {"type": "string", "description": "令牌", "minLength": 1}, "host": {"type": "string", "description": "主机", "minLength": 1}}}, "CreateTaskResponse": {"type": "object", "properties": {"user_id": {"type": "integer", "format": "int64", "description": "用户ID"}, "password": {"type": "string", "description": "密码"}, "code": {"type": "string", "description": "响应代码"}, "msg": {"type": "string", "description": "响应消息"}}}, "UpdateTaskStatusRequest": {"type": "object", "required": ["status"], "properties": {"status": {"$ref": "#/components/schemas/TaskStatus"}}}, "Task": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "任务ID"}, "user_id": {"type": "integer", "format": "int64", "description": "用户ID"}, "credit_code": {"type": "string", "description": "信用代码"}, "status": {"$ref": "#/components/schemas/TaskStatus"}, "report_id": {"type": "string", "description": "报告ID"}, "created_at": {"type": "string", "format": "date-time", "description": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "description": "更新时间"}, "deleted_at": {"type": "string", "format": "date-time", "description": "删除时间", "nullable": true}}}, "TaskStatus": {"type": "string", "description": "任务状态", "enum": ["pending", "reviewing", "final_review", "completed"], "x-enum-descriptions": {"pending": "待确认", "reviewing": "审核中", "final_review": "待终审", "completed": "已完结"}}, "GetTaskResponse": {"type": "object", "properties": {"task": {"$ref": "#/components/schemas/Task"}}}, "UpdateTaskStatusResponse": {"type": "object", "properties": {"task": {"$ref": "#/components/schemas/Task"}}}, "ListTasksResponse": {"type": "object", "properties": {"tasks": {"type": "array", "description": "任务列表", "items": {"$ref": "#/components/schemas/Task"}}, "total": {"type": "integer", "format": "int64", "description": "总数"}}}, "SuccessResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码，0表示成功", "example": 0}}}, "ErrorResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "错误码"}, "message": {"type": "string", "description": "错误信息"}}}}}, "tags": [{"name": "任务管理", "description": "任务相关的管理操作"}]}